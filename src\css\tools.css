/* ===== TOOLS SECTION STYLES ===== */
/* Styles specific to the tools page */

#tools {
  background-color: #ffffff;
  padding-top: 0;
}

.tools-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 180px 0;
  gap: 64px;
}

/* Tools headline styles */
.tools-headline {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
}

.tools-header-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

#scan-history-btn {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 1000 !important;
  display: none;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.9rem;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

#scan-history-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Show scan history button only when tools section is visible */
#tools:not([style*="display: none"]) #scan-history-btn {
  display: flex !important;
}

.tools-title {
  font-family: var(--font-family);
  font-weight: 600;
  font-size: 61px;
  line-height: 1.4em;
  letter-spacing: -1.64%;
  text-align: center;
  color: #2d3339;
  max-width: 1106px;
  margin-bottom: var(--spacing-md);
}

.tools-subtitle {
  font-family: var(--font-family);
  font-weight: 400;
  font-size: 1.2rem;
  line-height: 1.5;
  text-align: center;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

/* Tools content styles */
.tools-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 48px;
}

.tools-content-headline {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  gap: 10px;
}

.tools-content-title {
  font-family: var(--font-family);
  font-weight: 500;
  font-size: 31px;
  line-height: 1.5em;
  letter-spacing: -1.61%;
  text-align: center;
  color: #40474f;
  width: 100%;
}

.tools-content-description {
  font-family: var(--font-family);
  font-weight: 400;
  font-size: 20px;
  line-height: 1.6em;
  letter-spacing: -1.25%;
  text-align: center;
  color: #828f9b;
  max-width: 754px;
}

/* File upload section */
.tools-file-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60%;
  gap: 12px;
  padding: 32px 32px 0px 32px;
  border-radius: 12px;
  border: 1px solid #d2d6db;
}

.scan-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  /* margin-bottom: var(--spacing-lg); */
  width: 100%;
  max-width: 800px;
}

.scan-option {
  flex: 1;
  /* max-width: 328px; */
  height: 200px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  /* box-shadow: var(--shadow-md); */

  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #d2d6db;
}

.scan-option:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.scan-option.active {
  border-color: var(--primary-color);
}

.scan-option-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.scan-option-title {
  font-size: 1rem;
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.scan-option-description {
  color: var(--text-color);
  font-size: 0.9rem;
}

.scan-interface {
  width: 100%;
  max-width: 800px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  padding-top: 12px;
  /* box-shadow: var(--shadow-md); */
  margin-bottom: var(--spacing-lg);
}

.camera-container {
  width: 100%;
  aspect-ratio: 4/3;
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  margin-bottom: var(--spacing-md);
  border: 1px solid #d2d6db;
}

.camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 1.2rem;
  text-align: center;
}

.camera-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.camera-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  background-color: var(--primary-color);
  color: var(--light-text);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.camera-btn:hover {
  background-color: #0d1b69;
}

.camera-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.file-upload-area {
  border: 2px dashed #d2d6db;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
}

.file-upload-area.dragover {
  border-color: var(--secondary-color);
  background-color: rgba(41, 122, 254, 0.1);
}

.upload-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.upload-text {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.upload-hint {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: var(--spacing-sm);
}

.upload-info {
  font-size: 0.8rem;
  color: #888;
  margin-bottom: var(--spacing-md);
}

.file-input {
  display: none;
}

.analyze-btn {
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--secondary-color);
  color: var(--light-text);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.analyze-btn:hover {
  background-color: #1e88e5;
}

.analyze-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Results section */
.results-section {
  width: 100%;
  max-width: 800px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.result-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.result-title {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  text-align: left;
}

.result-status {
  font-size: 1.1rem;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  display: inline-block;
}

.result-status.normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.result-status.anemic {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
}

.result-details {
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);

}

.result-metric {
  background-color: var(--background-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: var(--spacing-xs);
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.result-actions .btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.6;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid #297afe;
}

.result-actions .btn-secondary {
  background: #bbc2c9;
  color: #2d3339;
  border: 1px solid #297afe;
  width: 211px;
}

.result-actions .btn-primary {
  background: #297afe;
  color: #ffffff;
  border: 1px solid #297afe;
  flex: 1;
}

/* Eye placeholder styles */
.eye-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 150px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 2;
}

.eye-placeholder.active {
  opacity: 0.7;
}

.eye-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: invert(1);
}

/* Camera overlay improvements */
.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  text-align: center;
  z-index: 3;
}

.camera-icon {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-md);
}

.camera-message {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
}

.camera-hint {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-lg);
  max-width: 300px;
  line-height: 1.4;
}

/* Upload interface styles */
#upload-interface {
  display: none;
}

.file-upload-area {
  border: 2px dashed #d2d6db;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(26, 35, 126, 0.05);
}

.file-upload-area.dragover {
  border-color: var(--secondary-color);
  background-color: rgba(41, 122, 254, 0.1);
  transform: scale(1.02);
}

/* Preview container styles */
.preview-container {
  display: none;
  width: 100%;
  max-width: 60%;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  border: 1px solid #d2d6db;
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  margin-top: var(--spacing-lg);
  text-align: center;
}

.heading-preview {
  width: 100%;
  margin-bottom: 24px;
}

.preview-image {
  width: 100%;
  /* max-width: 500px; */
  height: auto;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
}

.preview-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.preview-controls .btn {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  min-width: 150px;
}

/* Result container styles */
.result-container {
  display: none;
  width: 60%;
  max-width: 900px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 40px;
  /* box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05); */
  margin: 40px auto;
  text-align: center;
  border: 1px solid #d2d6db;
  position: relative;
  overflow: hidden;
}

.result-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  /* background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)); */
  border-radius: 20px 20px 0 0;

}

.result-image-container {
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
  position: relative;
}

.result-image {
  width: 100%;
  max-height: 400px;
  border-radius: 16px;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  object-fit: cover;
  border: 3px solid #ffffff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-image:hover {
  transform: scale(1.02);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
}

.result-box {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 24px;
  gap: 32px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  margin-bottom: 32px;
  backdrop-filter: blur(10px);
  border: 1px solid #297afe;
}

/* Background hijau untuk No Anemia Detected */
.result-box.no-anemia {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(129, 199, 132, 0.15));
  border: 1px solid rgba(76, 175, 80, 0.3);
}

/* Background kuning untuk Anemia Detected */
.result-box.anemia-detected {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.15), rgba(255, 183, 77, 0.15));
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.result-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  flex: 1;
}

.result-icon {
  font-size: 4.5rem;
  margin-bottom: 8px;
  padding: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}

.result-icon.loading {
  color: var(--secondary-color);
  background: linear-gradient(135deg, rgba(41, 122, 254, 0.1), rgba(41, 122, 254, 0.2));
  animation: pulse 2s infinite;
}

.result-icon.success {
  color: var(--success-color);
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.2));
}

.result-icon.warning {
  color: #ff9800;
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.2));
}

.result-title {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1a1a1a;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.result-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #5a6c7d;
  text-align: left;
  margin-bottom: 0;
  font-weight: 400;
  opacity: 0.9;
}

.result-details {
  /* background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); */
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid #d2d6db;
  /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05); */
  backdrop-filter: blur(10px);
}

/* Scan Details Heading */
.scan-details-heading {
  color: #1a1a1a;
  margin-bottom: 24px;
  font-size: 1.5rem;
  text-align: left;
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.scan-details-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  /* background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)); */
  border-radius: 2px;
}

/* Scan Details Container */
.scan-details-container {
  /* background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%); */
  border-radius: 16px;
  /* padding: 28px; */
  /* margin-bottom: 24px; */
  border: 1px solid rgba(255, 255, 255, 0.6);
  /* box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05); */
}

/* Scan Info Grid */
.scan-info-grid {
  display: flex;
  flex-direction: column;
  /* grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); */
  /* gap: 20px; */
  /* margin-bottom: 32px; */
}

.scan-info-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px 0px;
  /* border-radius: 12px; */
  text-align: left;
  border: 1px solid rgba(255, 255, 255, 0.8);
  /* box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08); */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.scan-info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  /* background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)); */
  border-top: 1px solid #d2d6db;;
}

/* .scan-info-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
} */

.scan-info-label {
  display: block;
  font-weight: 600;
  color: #838383;;
  margin-bottom: 8px;
  font-size: 0.6rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

.scan-info-value {
  display: block;
  font-size: 1.4rem;
  color: var(--text-color);
  font-weight: 700;
  line-height: 1.2;
}

.result-details ul {
  list-style: none;
  padding: 0;
}

.result-details li {
  padding: var(--spacing-sm) 0;
  border-top: 1px solid #d2d6db;;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-details li:last-child {
  border-bottom: none;
}

/* Recommendations Section */
.recommendations-section {
  /* background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); */
  border-radius: 16px;
  padding-top: 32px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08); */
  /* gap: 20px; */
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.recommendations-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  /* background: linear-gradient(90deg, #ff9800, #f57c00); */
}

.recommendations-title {
  color: #1a1a1a;
  margin-bottom: 24px;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: left;
  /* text-transform: uppercase; */
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 12px;
}

.recommendations-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  /* background: linear-gradient(90deg, #ff9800, #f57c00); */
  border-radius: 1px;
}

.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
  /* gap: 16px; */
  display: flex;
  flex-direction: column;
}

.recommendations-list p {
  padding-bottom: 24px;
  margin-bottom: 0;
  /* background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%); */
  /* border-radius: 12px; */
  color: var(--text-color);
  text-align: justify;
  font-size: 1rem;
  
  line-height: 1.6;
  position: relative;
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); */
  transition: all 0.3s ease;
}

.recommendations-list li {
  content: "-";
  padding: 24px 0px;
  margin-bottom: 0;
  /* background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%); */
  /* border-radius: 12px; */
  color: var(--text-color);
  /* border-left: 4px solid #ff9800; */
  font-size: 1rem;
  line-height: 1.6;
  position: relative;
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05); */
  transition: all 0.3s ease;
}

/* .recommendations-list li:hover {
  transform: translateX(4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
} */

.recommendations-list li:before {
  /* content: "-"; */
  position: absolute;
  left: -2px;
  top: 20px;
  font-size: 1.2rem;
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* When only back button is visible, center it */
.result-actions:has(#chat-ai-btn[style*="display: none"]) {
  justify-content: center;
}

.result-actions .btn {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
}

.result-actions .btn i {
  font-size: 1.1rem;
}

/* Tools notes section */
.tools-notes {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  margin-top: 12px;
  margin-left: auto;
  margin-right: auto;
}

.warning-circle {
  width: 24px;
  height: 24px;
  background-color: #ffc107;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.warning-circle i {
  color: white;
  font-size: 1.2rem;
}

.tools-notes-text {
  color: #856404;
  font-weight: 500;
  margin: 0;
  font-size: 0.8rem;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: var(--border-radius-md);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--primary-color);
  font-weight: 500;
  text-align: center;
}

/* Progress Bar untuk Loading */
.progress-container {
  width: 100%;
  /* max-width: 500px; */
  margin: 32px auto 0;
  margin-bottom: 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
  border-radius: 16px;
  padding: 24px;
  /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); */
  /* border: 1px solid #d2d6db; */
  backdrop-filter: blur(10px);
}

.progress-bar-wrapper {
  width: 100%;
  height: 10px;
  background: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  margin-bottom: 16px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background: var(--primary-color);
  border-radius: 6px;
  width: 0%;
  transition: width 0.3s ease-out;
  position: relative;
}

.progress-text {
  text-align: center;
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 20px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background: #e9ecef;
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.6;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px;
  border-radius: 8px;
}

.progress-step.active {
  opacity: 1;
  color: var(--primary-color);
  font-weight: 600;
  transform: scale(1.05);
}

.progress-step.completed {
  opacity: 1;
  color: var(--success-color);
  font-weight: 600;
}

.step-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-step.active .step-icon {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  animation: pulse 2s infinite;
  box-shadow: 0 4px 8px rgba(26, 35, 126, 0.3);
}

.progress-step.completed .step-icon {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.progress-step.completed .step-icon::before {
  content: '✓';
  font-size: 0.9rem;
  font-weight: bold;
}

.progress-step span {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-height: 16px;
  line-height: 1.2;
}



/* Responsive design untuk progress bar */
@media (max-width: 768px) {
  .progress-container {
    max-width: 100%;
    margin: 24px 16px 0;
    padding: 20px;
  }

  .progress-steps {
    margin-top: 16px;
  }

  .progress-step {
    padding: 2px;
  }

  .progress-step span {
    font-size: 0.7rem;
  }

  .step-icon {
    width: 26px;
    height: 26px;
    font-size: 0.75rem;
  }

  .progress-text {
    font-size: 0.9rem;
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .progress-container {
    margin: 20px 12px 0;
    padding: 16px;
    border-radius: 12px;
  }

  .progress-bar-wrapper {
    height: 8px;
    margin-bottom: 12px;
  }

  .progress-text {
    font-size: 0.85rem;
    margin-bottom: 14px;
  }

  .progress-steps {
    margin-top: 14px;
    gap: 4px;
  }

  .progress-step {
    padding: 2px;
    gap: 6px;
  }

  .progress-step span {
    font-size: 0.65rem;
    line-height: 1.1;
  }

  .step-icon {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    border-width: 1px;
  }

  .progress-steps::before {
    top: 12px;
  }
}

/* Message states */
.message {
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  text-align: center;
  font-weight: 500;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.message.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #d32f2f;
}

.success-message {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  padding: 24px 4px;
  border-radius: 4px;
  color: #388e3c;
}

.info-message {
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  color: #1976d2;
}

/* Button improvements */
.btn {
  border: none;
  cursor: pointer;
  font-family: var(--font-family);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: 1px solid var(--primary-color);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0d1b69, #1e88e5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
}

.btn-secondary {
  background: #f8f9fa;
  color: var(--primary-color);
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn:disabled:before {
  display: none;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tools-container {
    padding: 110px 32px;
    gap: var(--spacing-xl);
  }

  .tools-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .tools-content-title {
    font-size: 1.8rem;
  }

  .tools-content-description {
    font-size: 1rem;
    padding: 0 var(--spacing-md);
  }

  .tools-file-upload{
    
    width: 100%;
  }

  .scan-option {
    max-width: 50%;
  }

  .scan-interface {
    padding: 0px;
  }

  .camera-container {
    aspect-ratio: 3/4;
  }

  .camera-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .camera-controls .btn {
    width: 100%;
  }

  .file-upload-area {
    min-height: 250px;
    padding: var(--spacing-lg);
  }

  .upload-icon {
    font-size: 2rem;
  }

  .upload-text {
    font-size: 1rem;
  }

  .preview-controls {
    flex-direction: column;
  }

  .preview-controls .btn {
    width: 100%;
    min-width: auto;
  }

  .result-container {
    padding: 24px;
    margin: 20px auto;
    border-radius: 16px;
  }

  .result-box {
    flex-direction: column;
    gap: 20px;
    padding: 24px 16px;
    text-align: center;
  }

  .result-text {
    align-items: center;
    text-align: center;
  }

  .result-icon {
    width: 80px;
    height: 80px;
    font-size: 3rem;
    margin-bottom: 0;
  }

  .result-title {
    font-size: 1.8rem;
  }

  .result-description {
    font-size: 1rem;
    text-align: center;
  }

  .result-details {
    padding: 24px;
  }

  .scan-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .scan-info-item {
    padding: 20px 16px;
  }

  .recommendations-section {
    padding: 24px;
  }

  .recommendations-list li {
    padding: 16px 20px;
  }

  .result-actions {
    flex-direction: column;
  }

  .result-actions .btn {
    width: 100%;
    min-width: auto;
  }

  .tools-notes {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-md);
  }

  .eye-placeholder {
    width: 150px;
    height: 120px;
  }
}

@media (max-width: 480px) {
  .section{
    max-width:100%;
    
  }
  .tools-container {
    padding: 60px var(--spacing-sm);
    max-width:100%;
    gap: var(--spacing-md);
  }

  .tools-title {
    font-size: 2rem;
    margin-top: 64px;
  }

  .tools-subtitle {
    font-size: 1rem;
    padding: 4px
  }

  .tools-content-title {
    font-size: 1.5rem;
  }

  .tools-content-description {
    font-size: 0.9rem;
  }

  .scan-interface {
    padding: var(--spacing-sm);
  }

  .camera-container {
    border-radius: var(--border-radius-md);
  }

  .file-upload-area {
    min-height: 200px;
    padding: var(--spacing-md);
  }

  .upload-icon {
    font-size: 1.5rem;
  }

  .upload-text {
    font-size: 0.9rem;
  }

  .upload-hint {
    font-size: 0.8rem;
  }

  .result-container {
    padding: 20px;
    margin: 16px auto;
    border-radius: 12px;
  }

  .result-box {
    padding: 20px 12px;
    gap: 16px;
  }

  .result-icon {
    width: 70px;
    height: 70px;
    font-size: 2.5rem;
  }

  .result-title {
    font-size: 1.5rem;
  }

  .result-description {
    font-size: 0.95rem;
  }

  .result-details {
    padding: 20px;
  }

  .scan-details-heading {
    font-size: 1.2rem;
  }

  .scan-info-item {
    padding: 16px 12px;
  }

  .scan-info-value {
    font-size: 1.2rem;
  }

  .recommendations-section {
    padding: 20px;
  }

  .recommendations-title {
    font-size: 1.1rem;
  }

  .recommendations-list li {
    padding: 14px 16px;
    font-size: 0.9rem;
  }

  .tools-notes {
    padding: var(--spacing-sm);
  }

  .tools-notes-text {
    font-size: 0.85rem;
  }

  .warning-circle {
    width: 35px;
    height: 35px;
  }

  .warning-circle i {
    font-size: 1rem;
  }

  .preview-container {
    max-width: 90%;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .eye-placeholder img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}



/* Print styles */
@media print {
  .tools-container {
    padding: 0;
  }

  .camera-controls,
  .preview-controls,
  .result-actions {
    display: none;
  }

  .scan-interface,
  .result-container {
    box-shadow: none;
    border: 1px solid #ccc;
  }


}
