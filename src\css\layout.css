/* ===== LAYOUT ===== */
/* Layout and container styles */

/* Container */
.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 24px 84px;
}

/* Responsive container adjustments */
@media (max-width: 1200px) {
  .container {
    padding: 20px 40px;
  }
}

/* Sections */
.section {
  padding: var(--spacing-xl) 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* SPA specific styles */
.login-page-active,
.register-page-active {
  overflow: hidden;
}

.login-page-active #header,
.login-page-active #footer,
.register-page-active #header,
.register-page-active #footer {
  display: none !important;
}

/* Footer */
footer {
  background-color: #ffffff;
  color: #333333;
  padding: 60px 0 20px 0;
  border-top: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.footer-wrapper {
  position: relative;
  width: 100%;
}

.footer-background-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: clamp(8rem, 20vw, 24rem);
  font-weight: 900;
  color: #f8f9fa;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  z-index: 1;
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  opacity: 0.8;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-brand {
  max-width: 300px;
}

.footer-brand-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
  letter-spacing: 0.05em;
}

.footer-description {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 20px;
}

.footer-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: #6b7280;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #374151;
}

.social-links {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.social-link i {
  font-size: 16px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  position: relative;
  z-index: 2;
}

.copyright {
  text-align: left;
}

.copyright p {
  font-size: 0.85rem;
  color: #6b7280;
  margin: 0;
}

.footer-logo {
  text-align: right;
}

.footer-logo-text {
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: 500;
}

/* Basic responsive layout adjustments */
@media (max-width: 1024px) {
  .container {
    padding: 16px 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px 20px;
  }

  .section {
    padding: var(--spacing-lg) 0;
  }

  /* Footer responsive */
  footer {
    padding: 40px 0 20px 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 30px;
  }

  .footer-brand {
    max-width: 100%;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer-logo {
    text-align: center;
  }

  /* Background text responsive */
  .footer-background-text {
    font-size: clamp(6rem, 15vw, 18rem);
    opacity: 0.6;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px 16px;
  }

  .section {
    padding: var(--spacing-md) 0;
  }

  /* Footer mobile */
  footer {
    padding: 30px 0 15px 0;
  }

  .footer-content {
    gap: 25px;
    margin-bottom: 25px;
  }

  .footer-brand-title {
    font-size: 1.3rem;
  }

  .footer-description {
    font-size: 0.85rem;
  }

  .footer-title {
    font-size: 0.95rem;
  }

  .footer-links a {
    font-size: 0.85rem;
  }

  .social-links {
    gap: 10px;
  }

  .social-link {
    width: 32px;
    height: 32px;
  }

  .social-link i {
    font-size: 14px;
  }

  .copyright p,
  .footer-logo-text {
    font-size: 0.8rem;
  }

  /* Background text mobile */
  .footer-background-text {
    font-size: clamp(4rem, 12vw, 12rem);
    opacity: 0.4;
  }
}
