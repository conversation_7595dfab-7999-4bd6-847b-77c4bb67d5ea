/* ===== LAYOUT ===== */
/* Layout and container styles */

/* Container */
.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 24px 84px;
}

/* Responsive container adjustments */
@media (max-width: 1200px) {
  .container {
    padding: 20px 40px;
  }
}

/* Sections */
.section {
  padding: var(--spacing-xl) 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* SPA specific styles */
.login-page-active,
.register-page-active {
  overflow: hidden;
}

.login-page-active #header,
.login-page-active #footer,
.register-page-active #header,
.register-page-active #footer {
  display: none !important;
}

/* Footer */
footer {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: var(--spacing-lg) 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.footer-section {
  flex: 0;
  min-width: 250px;
  margin-bottom: var(--spacing-lg);
}

.footer-title {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--light-text);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.footer-links a:hover {
  opacity: 1;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: 12px;
}

.social-links a {
  color: var(--light-text);
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: var(--secondary-color);
}

.copyright {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
  font-size: 0.9rem;
  padding: var(--spacing-md) 0;
}

.footer-text-product {
  font-weight: 1000;
}

/* Basic responsive layout adjustments */
@media (max-width: 1024px) {
  .container {
    padding: 16px 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px 20px;
  }

  .section {
    padding: var(--spacing-lg) 0;
  }
  .footer-content {
  display: grid;
}
  .copyright p {
    font-size: 6rem;
    padding: var(--spacing-md) 0;
  

}
}

@media (max-width: 480px) {
  .container {
    padding: 12px 16px;
  }

  .section {
    padding: var(--spacing-md) 0;
  }
}
