/* ===== NAVIGATION ===== */
/* Header, navbar, and navigation styles */

/* Header and Navigation */
header {
  background-color: transparent;
  color: var(--text-color);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 24px 84px;
  transition: padding 0.3s ease;
}

.nav-container {
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: none; /* Remove container max-width constraint */
  margin: 0; /* Remove container margin */
}

.navbar {
  background-color: #ffffff;
  border: 0.5px solid #d2d6db;
  border-radius: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  gap: 10px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin: 0 auto;
  max-width: none;
}

/* Shrunk navbar state */
.navbar-shrunk {
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
  margin: 0 auto;
  transform: translateX(0);
}

.logo {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 700;
  font-size: 18px;
  color: #2d3339;
  line-height: 1.6em;
  white-space: nowrap;
  transition: font-size 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 40px;
  margin: 0;
  flex: 1;
  justify-content: center;
  align-items: center;
  transition: gap 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Shrunk nav links state */
.navbar-shrunk .nav-links {
  gap: 24px;
}

.nav-links li a {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #2d3339;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-links a:hover {
  color: #297afe;
  transform: translateY(-1px);
}

.login-btn {
  background-color: #297afe;
  color: #e8ebed;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 700;
  font-size: 16px;
  padding: 8px 24px;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-btn:hover {
  background-color: #1a6be0;
}

/* Navigation Install Button - Hidden */
.nav-install-btn {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  display: none !important; /* Hidden by default */
  align-items: center;
  gap: 6px;
  margin-right: 12px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.nav-install-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  background: linear-gradient(135deg, #45a049 0%, #388e3c 100%);
}

.nav-install-btn i {
  font-size: 12px;
}

.nav-install-btn span {
  font-size: 14px;
}

.mobile-login {
  display: none;
}

.desktop-only {
  display: block;
}

.nav-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

/* Mobile menu styles */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: #2d3339;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
}

/* User Profile Styles */
.user-profile-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 8px; /* Add some spacing */
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 16px;
  transition: background-color 0.3s ease;
}

.profile-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.profile-image {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
}

.profile-name {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #2d3339;
}

.profile-arrow {
  font-size: 12px;
  color: #2d3339;
  transition: transform 0.3s ease;
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 240px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
  z-index: 1000;
}

.profile-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-profile-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
}

.dropdown-user-info {
  display: flex;
  flex-direction: column;
}

.dropdown-user-name {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #2d3339;
}

.dropdown-user-email {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #6c757d;
}

.dropdown-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 0;
}

.dropdown-menu {
  list-style: none;
  padding: 8px 0;
  margin: 0;
}

.dropdown-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item i {
  font-size: 16px;
  color: #6c757d;
}

.dropdown-item span {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #2d3339;
}

.logout-btn {
  color: #dc3545;
}

.logout-btn i {
  color: #dc3545;
}

/* Mobile profile styles */
.user-profile-container.mobile {
  width: 100%;
  margin-left: 0;
  margin-bottom: 8px;
}

.profile-button.mobile {
  width: 100%;
  justify-content: flex-start;
  padding: 8px;
}

.profile-dropdown.mobile {
  position: relative;
  width: 100%;
  margin-top: 8px;
  left: 0;
  right: auto;
}

/* ===== RESPONSIVE NAVBAR STYLES ===== */

/* Medium tablets and small desktops */
@media (max-width: 1024px) {
  header {
    padding: 20px 40px;
  }

  .nav-container {
    padding: 0 20px; /* Add container padding for medium screens */
  }

  .navbar {
    padding: 14px 20px;
    gap: 12px;
  }

  .logo {
    font-size: 17px;
  }

  .nav-links {
    gap: 32px;
  }

  .nav-links li a {
    font-size: 15px;
  }
}

/* Tablets and large mobile devices */
@media (max-width: 768px) {
  header {
    padding: 18px 16px;
  }

  .nav-container {
    padding: 0 16px; /* Add container padding for tablets */
  }

  .navbar {
    padding: 12px 16px;
    gap: 12px;
  }

  .logo {
    font-size: 16px;
  }

  .mobile-menu-btn {
    display: block;
    padding: 8px;
    font-size: 1.4rem;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nav-links {
    position: fixed;
    top: 70px;
    left: 16px;
    right: 16px;
    width: auto;
    background-color: white;
    flex-direction: column;
    padding: 16px;
    border-radius: 16px;
    margin-top: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    gap: 20px;
  }

  .nav-links li a {
    font-size: 16px;
    padding: 10px 16px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .nav-links li a:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .mobile-login {
    display: none;
    width: 100%;
    /* margin-top: 12px; */
    /* padding-top: 16px; */
    /* border-top: 1px solid #e5e7eb; */
  }

  .mobile {
    display: none;
  }

  .mobile-login .login-btn {
    width: 100%;
    padding: 10px 20px;
    font-size: 15px;
    text-align: center;
    border-radius: 8px;
  }

  .desktop-only {
    display: none;
  }

  .nav-install-btn {
    display: none !important;
  }
}

/* Small mobile devices - 480px and below */
@media (max-width: 480px) {
  header {
    padding: 16px 12px;
  }

  .nav-container {
    padding: 0 12px; /* Add container padding for small mobile */
  }

  .navbar {
    padding: 8px 12px;
    gap: 10px;
    border-radius: 22px;
  }

  .logo {
    font-size: 16px;
  }

  .mobile-menu-btn {
    padding: 7px;
    font-size: 1.35rem;
    min-width: 38px;
    min-height: 38px;
  }

  .nav-links {
    top: 65px;
    left: 12px;
    right: 12px;
    padding: 14px;
    border-radius: 14px;
    margin-top: 6px;
  }

  .nav-links.active {
    gap: 18px;
  }

  .nav-links li a {
    font-size: 15px;
    padding: 9px 14px;
  }

  .mobile-login .login-btn {
    padding: 9px 18px;
    font-size: 14px;
  }
}

/* Very small mobile devices - 425px and below */
@media (max-width: 425px),
       screen and (max-device-width: 425px) {
  header {
    padding: 14px 8px !important;
    /* Disable any scroll-based transformations */
    transform: none !important;
  }

  .nav-container {
    padding: 0 8px !important; /* Add container padding for very small mobile */
    max-width: none !important;
    margin: 0 !important;
  }

  .navbar {
    padding: 6px 8px !important;
    gap: 8px !important;
    border-radius: 20px !important;
    /* Keep navbar stable - no scroll animations */
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    transition: none !important;
    margin: 0 auto !important;
  }

  /* Override any shrunk state styles */
  .navbar-shrunk {
    width: 100% !important;
    padding: 6px 8px !important;
    transform: none !important;
    margin: 0 !important;
    max-width: none !important;
    border-radius: 20px !important;
  }

  .logo {
    font-size: 15px;
  }

  .mobile-menu-btn {
    display: block;
    padding: 6px;
    font-size: 1.3rem;
    min-width: 36px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nav-links {
    position: fixed;
    top: 58px;
    left: 8px;
    right: 8px;
    width: auto;
    background-color: white;
    flex-direction: column;
    padding: 12px;
    border-radius: 12px;
    margin-top: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    gap: 16px;
  }

  .nav-links li a {
    font-size: 15px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .nav-links li a:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .mobile-login {
    display: none;
    width: 100%;
    margin-top: 8px;
    padding-top: 12px;
    border-top: 1px solid #e5e7eb;
  }

  .mobile {
    display: none;
  }

  .mobile-login .login-btn {
    width: 100%;
    padding: 8px 16px;
    font-size: 14px;
    text-align: center;
    border-radius: 8px;
  }

  .desktop-only {
    display: none;
  }

  .nav-install-btn {
    display: none !important;
  }

  /* User profile adjustments for very small screens */
  .user-profile-container {
    margin-left: 4px;
  }

  .profile-button {
    padding: 2px 8px;
    gap: 6px;
  }

  .profile-image {
    width: 32px;
    height: 32px;
  }

  .profile-username {
    font-size: 13px;
  }
}

/* Extra small mobile devices - 375px and below */
@media (max-width: 375px),
       screen and (max-device-width: 375px) {
  header {
    padding: 12px 6px !important;
  }

  .nav-container {
    padding: 0 6px !important; /* Add container padding for extra small mobile */
    max-width: none !important;
    margin: 0 !important;
  }

  .navbar {
    padding: 5px 6px !important;
    gap: 6px !important;
    border-radius: 18px !important;
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    transition: none !important;
    margin: 0 auto !important;
  }

  .navbar-shrunk {
    padding: 5px 6px !important;
    border-radius: 18px !important;
  }

  .logo {
    font-size: 14px;
  }

  .mobile-menu-btn {
    padding: 5px;
    font-size: 1.2rem;
    min-width: 34px;
    min-height: 34px;
  }

  .nav-links {
    top: 54px;
    left: 6px;
    right: 6px;
    padding: 10px;
    border-radius: 10px;
    margin-top: 3px;
  }

  .nav-links.active {
    gap: 14px;
  }

  .nav-links li a {
    font-size: 14px;
    padding: 7px 10px;
  }

  .mobile-login .login-btn {
    padding: 7px 14px;
    font-size: 13px;
  }

  .user-profile-container {
    margin-left: 2px;
  }

  .profile-button {
    padding: 1px 6px;
    gap: 4px;
  }

  .profile-image {
    width: 30px;
    height: 30px;
  }

  .profile-username {
    font-size: 12px;
  }
}

/* ===== DEVICE EMULATION SPECIFIC FIXES ===== */
/* These rules specifically target device emulation in developer tools */

/* Force responsive behavior for common mobile device widths */
@media screen and (max-width: 428px), /* iPhone 14 Pro Max */
       screen and (max-width: 414px), /* iPhone 11 Pro Max, iPhone XS Max */
       screen and (max-width: 390px), /* iPhone 14, iPhone 13 Pro */
       screen and (max-width: 375px), /* iPhone SE, iPhone 12 mini */
       screen and (max-width: 360px), /* Galaxy S8, S9 */
       screen and (max-width: 320px)  /* iPhone 5/SE */
{
  header {
    padding: 12px 6px !important;
    transform: none !important;
  }

  .nav-container {
    padding: 0 6px !important;
    max-width: none !important;
    margin: 0 !important;
    width: 100% !important;
  }

  .navbar {
    padding: 5px 6px !important;
    gap: 6px !important;
    border-radius: 18px !important;
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    transition: none !important;
    margin: 0 !important;
  }

  .navbar-shrunk {
    padding: 5px 6px !important;
    border-radius: 18px !important;
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    margin: 0 !important;
  }

  .logo {
    font-size: 14px !important;
  }

  .mobile-menu-btn {
    padding: 4px !important;
    font-size: 1.1rem !important;
    min-width: 32px !important;
    min-height: 32px !important;
  }
}

/* Additional fix for very specific device emulation issues */
@media only screen and (max-device-width: 480px) {
  .nav-container {
    padding: 0 8px !important;
    max-width: none !important;
    margin: 0 !important;
  }

  .navbar {
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
    margin: 0 !important;
  }
}



/* Force override for any conflicting styles */
@media screen and (max-width: 425px) {
  header[id="header"] {
    padding: 12px 6px !important;
  }

  .nav-container {
    padding: 0 6px !important;
    max-width: none !important;
    margin: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .navbar {
    padding: 5px 6px !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
}

