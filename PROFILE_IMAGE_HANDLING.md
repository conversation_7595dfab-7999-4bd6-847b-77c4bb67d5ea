# Profile Image Handling Documentation

## Overview
This document explains how profile images are handled in the Anevia application, including the fallback mechanism when external images fail to load.

## Image Source Priority
The application uses the following priority order for profile images:

1. **Backend uploaded image** (highest priority)
   - User-uploaded images stored on the backend server
   - Path format: `https://server.anevia.my.id/profiles/photo-{uid}.jpg`

2. **Firebase provider image** (medium priority)
   - Images from authentication providers (Google, etc.)
   - Usually Google Photos or Google Account profile pictures
   - Format: Full external URLs

3. **Default avatar** (fallback)
   - Local SVG file: `/src/assets/default-avatar.svg`
   - Always available as final fallback

## Normal Behavior: Image Loading Failures

### Expected Console Messages
When external images (Google Photos, etc.) fail to load, you may see these **normal** console messages:

```
google.com provider image unavailable, using default avatar
Firebase provider image unavailable, using default avatar
backend (external) image unavailable, using default avatar
```

### Why Images Fail to Load
External profile images may fail to load due to:

1. **Network connectivity issues**
2. **CORS restrictions** from external providers
3. **Expired or invalid URLs** from authentication providers
4. **Privacy settings** on user's Google account
5. **Rate limiting** from external services

## Implementation Details

### UserProfile.js
```javascript
setUserProfileImage(imageElement, user) {
  // Determines image source and sets appropriate fallback
  // Logs informative messages instead of warnings
}
```

### ProfileView.js
```javascript
setProfileImage(imageElement) {
  // Similar logic for profile page
  // Handles both backend and Firebase images
}
```

## Error Handling Strategy

### Graceful Degradation
- **Never show broken images** to users
- **Always fallback** to default avatar
- **Log informative messages** for debugging
- **Prevent infinite loops** with error handler cleanup

### User Experience
- Users see a consistent default avatar when external images fail
- No visual indication of "broken" images
- Seamless experience regardless of image source availability

## Debugging Profile Images

### Console Log Levels
- `console.log()` - Normal operation information
- `console.info()` - Image fallback notifications (not errors)
- `console.warn()` - Actual problems that need attention
- `console.error()` - Critical failures

### Troubleshooting Steps
1. Check console for image source information
2. Verify network connectivity
3. Test image URLs directly in browser
4. Check CORS policies for external images
5. Verify backend image upload functionality

## Best Practices

### For Developers
- **Don't treat image fallbacks as errors** - they're expected behavior
- **Monitor console.warn/error** for actual issues
- **Test with various authentication providers**
- **Ensure default avatar is always accessible**

### For Users
- Upload custom profile images for best reliability
- External provider images may not always be available
- Default avatar ensures consistent experience

## Related Files
- `src/js/components/UserProfile.js` - Navigation profile display
- `src/js/views/ProfileView.js` - Profile page display
- `src/js/models/ProfileModel.js` - Profile data management
- `src/main.js` - Main application profile handling
- `src/assets/default-avatar.svg` - Default fallback image

## Testing Scenarios
1. **Google login** with public profile photo
2. **Google login** with private profile photo
3. **Email login** with uploaded profile image
4. **Network disconnection** during image loading
5. **Invalid backend image URLs**

All scenarios should gracefully fallback to default avatar without showing broken images to users.
